/**
 * Receipt-Video Mapping Service
 * 
 * This service manages the mapping between receipt numbers and their associated video files.
 * It creates a complete audit trail linking each receipt number to the specific videos
 * uploaded during that user's session.
 * 
 * Features:
 * - Stores receipt-video mappings in AWS S3 at s3://icudatasetphrasesfortesting/receipt-numbers/
 * - Uses simple JSON format for the log file (receipt-log.json)
 * - Retroactively assigns receipt numbers to existing videos
 * - Links new videos to receipt numbers during upload process
 * - Maintains sequential 6-digit receipt numbering (000001, 000002, etc.)
 */

// CORS FIX: Use backend API instead of direct S3 calls to avoid CORS issues

// Backend configuration
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';

// Receipt log configuration
const RECEIPT_LOG_KEY = 'receipt-numbers/receipt-log.json';
const RECEIPT_BACKUP_KEY = 'receipt-numbers/receipt-log-backup.json';

console.log('✅ Receipt Mapping Service: Using backend API to avoid CORS issues');
console.log('🔧 Backend URL:', BACKEND_URL);

/**
 * Receipt Mapping Service
 */
export const receiptMappingService = {
  
  /**
   * Get the current receipt log via backend API (CORS FIX)
   * @returns {Promise<Object>} Receipt log object
   */
  async getReceiptLog() {
    try {
      console.log('📋 Fetching receipt log via backend API...');

      const response = await fetch(`${BACKEND_URL}/api/receipt-log`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          console.log('📋 Receipt log does not exist yet, starting with empty log');
          return {};
        }
        throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
      }

      const receiptLog = await response.json();
      console.log('✅ Receipt log fetched successfully:', Object.keys(receiptLog).length, 'receipts');
      return receiptLog;

    } catch (error) {
      console.error('❌ Error fetching receipt log via backend:', error);
      // Return empty log on error to allow the application to continue
      return {};
    }
  },

  /**
   * Save the receipt log via backend API (CORS FIX)
   * @param {Object} receiptLog - The receipt log object to save
   * @returns {Promise<boolean>} Success status
   */
  async saveReceiptLog(receiptLog) {
    try {
      console.log('💾 Saving receipt log via backend API...');

      const response = await fetch(`${BACKEND_URL}/api/receipt-log`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          receiptLog,
          metadata: {
            'last-updated': new Date().toISOString(),
            'total-receipts': Object.keys(receiptLog).length.toString(),
            'service-version': '1.0.0'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Receipt log saved successfully via backend');
      return result.success || true;

    } catch (error) {
      console.error('❌ Error saving receipt log via backend:', error);
      return false;
    }
  },

  /**
   * Scan existing videos via backend API and assign them to receipt 000001 (CORS FIX)
   * @returns {Promise<Object>} Assignment result
   */
  async assignReceiptToExistingVideos() {
    try {
      console.log('🔍 Scanning existing videos for receipt assignment via backend API...');

      // Use backend API to scan for existing videos
      const response = await fetch(`${BACKEND_URL}/api/scan-videos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scanPath: 'icu-videos/18to39/male/mixed/',
          maxKeys: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status} ${response.statusText}`);
      }

      const scanResult = await response.json();
      const videoFiles = scanResult.videoFiles || [];

      console.log(`📹 Found ${videoFiles.length} existing video files via backend`);

      if (videoFiles.length === 0) {
        console.log('📋 No existing videos found to assign');
        return { success: true, videosAssigned: 0, receiptNumber: null };
      }

      // Get current receipt log
      const receiptLog = await this.getReceiptLog();

      // Check if receipt 000001 already exists
      if (receiptLog['000001']) {
        console.log('📋 Receipt 000001 already exists, skipping assignment');
        return { 
          success: true, 
          videosAssigned: receiptLog['000001'].videos.length,
          receiptNumber: '000001',
          alreadyExists: true 
        };
      }

      // Create receipt 000001 entry
      const receiptEntry = {
        timestamp: new Date().toISOString(),
        videos: videoFiles,
        demographics: {
          age: '18to39',
          gender: 'male',
          ethnicity: 'mixed'
        },
        sessionId: 'useruser01', // Extracted from filename pattern
        assignmentType: 'retroactive',
        assignedAt: new Date().toISOString()
      };

      receiptLog['000001'] = receiptEntry;

      // Save updated receipt log
      const saveSuccess = await this.saveReceiptLog(receiptLog);
      
      if (saveSuccess) {
        // Update localStorage to ensure next receipt will be 000002
        localStorage.setItem('icuAppReceiptCounter', '1');
        console.log('✅ Receipt 000001 assigned to existing videos');
        console.log('🔢 Receipt counter set to 1 (next receipt will be 000002)');
        
        return {
          success: true,
          videosAssigned: videoFiles.length,
          receiptNumber: '000001',
          videos: videoFiles
        };
      } else {
        throw new Error('Failed to save receipt log');
      }
      
    } catch (error) {
      console.error('❌ Error assigning receipt to existing videos:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Add a new receipt-video mapping for a completed session
   * @param {string} receiptNumber - The receipt number (e.g., '000002')
   * @param {Array} videoUrls - Array of S3 URLs for the videos
   * @param {Object} demographics - User demographic information
   * @param {string} sessionId - Session identifier
   * @returns {Promise<boolean>} Success status
   */
  async addReceiptMapping(receiptNumber, videoUrls, demographics, sessionId) {
    try {
      console.log(`📋 Adding receipt mapping for ${receiptNumber}...`);
      console.log(`📹 Videos: ${videoUrls.length} files`);
      
      // Get current receipt log
      const receiptLog = await this.getReceiptLog();

      // Create new receipt entry
      const receiptEntry = {
        timestamp: new Date().toISOString(),
        videos: videoUrls,
        demographics: {
          age: demographics.ageGroup || demographics.age,
          gender: demographics.gender,
          ethnicity: demographics.ethnicity
        },
        sessionId: sessionId || demographics.userId || 'anonymous',
        assignmentType: 'prospective',
        recordingCount: videoUrls.length
      };

      receiptLog[receiptNumber] = receiptEntry;

      // Save updated receipt log
      const saveSuccess = await this.saveReceiptLog(receiptLog);
      
      if (saveSuccess) {
        console.log(`✅ Receipt ${receiptNumber} mapping saved successfully`);
        return true;
      } else {
        throw new Error('Failed to save receipt log');
      }
      
    } catch (error) {
      console.error(`❌ Error adding receipt mapping for ${receiptNumber}:`, error);
      return false;
    }
  },

  /**
   * Get videos associated with a specific receipt number
   * @param {string} receiptNumber - The receipt number to look up
   * @returns {Promise<Object|null>} Receipt data or null if not found
   */
  async getReceiptVideos(receiptNumber) {
    try {
      const receiptLog = await this.getReceiptLog();
      return receiptLog[receiptNumber] || null;
    } catch (error) {
      console.error(`❌ Error getting videos for receipt ${receiptNumber}:`, error);
      return null;
    }
  },

  /**
   * Get all receipt mappings
   * @returns {Promise<Object>} Complete receipt log
   */
  async getAllReceiptMappings() {
    return await this.getReceiptLog();
  },

  /**
   * Initialize the receipt mapping system
   * This should be called once when the application starts
   * @returns {Promise<Object>} Initialization result
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Receipt Mapping Service...');
      
      // Check if we can connect to S3
      if (!s3Client) {
        console.warn('⚠️ S3 client not available, receipt mapping will be limited');
        return { success: false, error: 'S3 client not initialized' };
      }

      // Try to fetch existing receipt log
      const receiptLog = await this.getReceiptLog();
      const existingReceipts = Object.keys(receiptLog).length;
      
      console.log(`📋 Found ${existingReceipts} existing receipts`);

      // If no receipts exist, try to assign existing videos to receipt 000001
      if (existingReceipts === 0) {
        console.log('🔄 No existing receipts found, checking for videos to assign...');
        const assignmentResult = await this.assignReceiptToExistingVideos();
        
        return {
          success: true,
          existingReceipts: existingReceipts,
          retroactiveAssignment: assignmentResult
        };
      }

      return {
        success: true,
        existingReceipts: existingReceipts,
        retroactiveAssignment: { success: true, videosAssigned: 0, alreadyExists: true }
      };
      
    } catch (error) {
      console.error('❌ Error initializing Receipt Mapping Service:', error);
      return { success: false, error: error.message };
    }
  }
};

export default receiptMappingService;
